import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ArrowDown, Mail, Download, Github, Linkedin } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";
import {
  ParticleSystem,
  LightTrail,
  FlowingWaves,
  MorphingShapes,
  ParallaxLayers,
  EnhancedButton,
  useRippleEffect
} from "@/components/VisualEffects";

export const Hero = () => {
  const { t } = useTranslation();
  const { iconLeft, isRTL } = useRTL();
  const { createRipple } = useRippleEffect();

  return (
    <section id="home" className="relative pt-20 min-h-screen flex items-center justify-center bg-white overflow-hidden">
      {/* Background Visual Effects */}
      <ParticleSystem />
      <LightTrail />
      <FlowingWaves />
      <MorphingShapes />
      <ParallaxLayers />

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-8">
          <Avatar className="w-32 h-32 mx-auto mb-6 ring-2 ring-gray-200 breathing-glow floating-element">
            <AvatarImage src="/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png" alt="Waleed Almshwly" />
            <AvatarFallback className="text-xl font-semibold bg-gray-900 text-white">
              WA
            </AvatarFallback>
          </Avatar>
        </div>

        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 text-reveal floating-element">
          <span className="gradient-text">{t("fullStackDeveloper")}</span>
        </h1>

        <p className="text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto floating-element">
          {t("heroDescription")}
        </p>

        <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 floating-element ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <EnhancedButton
            variant="liquid"
            className="px-8 py-3 rounded-lg text-lg font-semibold"
            onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
          >
            {t("viewMyWork")}
          </EnhancedButton>

          <EnhancedButton
            variant="secondary"
            className="px-8 py-3 rounded-lg text-lg font-semibold animated-border"
            onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
          >
            {t("getInTouch")}
          </EnhancedButton>

          <EnhancedButton
            variant="primary"
            className="px-8 py-3 rounded-lg text-lg font-semibold flex items-center gap-2"
            onClick={() => window.open('/resume.pdf', '_blank')}
          >
            <Download className={`w-4 h-4 ${iconLeft}`} />
            {t("downloadCV")}
          </EnhancedButton>
        </div>

        <div className={`flex justify-center gap-6 mb-12 floating-element ${isRTL ? 'flex-row-reverse' : ''}`}>
          <a
            href="https://github.com"
            className="p-3 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors magnetic-hover ripple-container"
            aria-label="GitHub Profile"
            onClick={(e) => createRipple(e)}
          >
            <Github className="h-6 w-6" />
          </a>
          <a
            href="https://linkedin.com"
            className="p-3 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors magnetic-hover ripple-container"
            aria-label="LinkedIn Profile"
            onClick={(e) => createRipple(e)}
          >
            <Linkedin className="h-6 w-6" />
          </a>
          <a
            href="mailto:<EMAIL>"
            className="p-3 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors magnetic-hover ripple-container"
            aria-label="Send Email"
            onClick={(e) => createRipple(e)}
          >
            <Mail className="h-6 w-6" />
          </a>
        </div>

        <div className="text-center floating-element">
          <a
            href="#about"
            className="inline-block p-3 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors magnetic-hover animate-bounce-slow ripple-container"
            onClick={(e) => {
              createRipple(e);
              document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            <ArrowDown className="h-6 w-6 text-gray-600" />
          </a>
        </div>
      </div>
    </section>
  );
};
